# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2024_05_08_172856) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_stat_statements"
  enable_extension "plpgsql"

  create_table "account_channels", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "account_types", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.integer "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", precision: nil, null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "activities", force: :cascade do |t|
    t.integer "kind", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.string "transactable_type", null: false
    t.bigint "transactable_id", null: false
    t.bigint "wallet_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["transactable_type", "transactable_id"], name: "index_activities_on_transactable"
    t.index ["wallet_id"], name: "index_activities_on_wallet_id"
  end

  create_table "addresses", force: :cascade do |t|
    t.string "line1", null: false
    t.string "line2"
    t.string "city", null: false
    t.string "zip_code", null: false
    t.bigint "state_id"
    t.string "addressable_type"
    t.bigint "addressable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "country_id", null: false
    t.float "latitude"
    t.float "longitude"
    t.index ["addressable_type", "addressable_id"], name: "index_addresses_on_addressable"
    t.index ["country_id"], name: "index_addresses_on_country_id"
    t.index ["latitude", "longitude"], name: "index_addresses_on_latitude_and_longitude"
    t.index ["state_id"], name: "index_addresses_on_state_id"
  end

  create_table "adjustments", force: :cascade do |t|
    t.integer "points"
    t.integer "kind", default: 0
    t.text "notes", null: false
    t.bigint "user_id", null: false
    t.bigint "admin_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_id"], name: "index_adjustments_on_admin_id"
    t.index ["user_id"], name: "index_adjustments_on_user_id"
  end

  create_table "ahoy_events", force: :cascade do |t|
    t.bigint "visit_id"
    t.bigint "user_id"
    t.string "name"
    t.jsonb "properties"
    t.datetime "time", precision: nil
    t.index ["name", "time"], name: "index_ahoy_events_on_name_and_time"
    t.index ["properties"], name: "index_ahoy_events_on_properties", opclass: :jsonb_path_ops, using: :gin
    t.index ["user_id"], name: "index_ahoy_events_on_user_id"
    t.index ["visit_id"], name: "index_ahoy_events_on_visit_id"
  end

  create_table "ahoy_visits", force: :cascade do |t|
    t.string "visit_token"
    t.string "visitor_token"
    t.bigint "user_id"
    t.string "ip"
    t.text "user_agent"
    t.text "referrer"
    t.string "referring_domain"
    t.text "landing_page"
    t.string "browser"
    t.string "os"
    t.string "device_type"
    t.string "country"
    t.string "region"
    t.string "city"
    t.float "latitude"
    t.float "longitude"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_term"
    t.string "utm_content"
    t.string "utm_campaign"
    t.string "app_version"
    t.string "os_version"
    t.string "platform"
    t.datetime "started_at", precision: nil
    t.index ["user_id"], name: "index_ahoy_visits_on_user_id"
    t.index ["visit_token"], name: "index_ahoy_visits_on_visit_token", unique: true
    t.index ["visitor_token", "started_at"], name: "index_ahoy_visits_on_visitor_token_and_started_at"
  end

  create_table "blazer_audits", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "query_id"
    t.text "statement"
    t.string "data_source"
    t.datetime "created_at", precision: nil
    t.index ["query_id"], name: "index_blazer_audits_on_query_id"
    t.index ["user_id"], name: "index_blazer_audits_on_user_id"
  end

  create_table "blazer_checks", force: :cascade do |t|
    t.bigint "creator_id"
    t.bigint "query_id"
    t.string "state"
    t.string "schedule"
    t.text "emails"
    t.text "slack_channels"
    t.string "check_type"
    t.text "message"
    t.datetime "last_run_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_blazer_checks_on_creator_id"
    t.index ["query_id"], name: "index_blazer_checks_on_query_id"
  end

  create_table "blazer_dashboard_queries", force: :cascade do |t|
    t.bigint "dashboard_id"
    t.bigint "query_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dashboard_id"], name: "index_blazer_dashboard_queries_on_dashboard_id"
    t.index ["query_id"], name: "index_blazer_dashboard_queries_on_query_id"
  end

  create_table "blazer_dashboards", force: :cascade do |t|
    t.bigint "creator_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_blazer_dashboards_on_creator_id"
  end

  create_table "blazer_queries", force: :cascade do |t|
    t.bigint "creator_id"
    t.string "name"
    t.text "description"
    t.text "statement"
    t.string "data_source"
    t.string "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_blazer_queries_on_creator_id"
  end

  create_table "brands", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "photo_sales_count"
    t.integer "optics_sales_count"
    t.integer "users_count", default: 0, null: false
    t.index ["name"], name: "index_brands_on_name"
  end

  create_table "brands_stores", id: false, force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "store_id", null: false
    t.index ["brand_id"], name: "index_brands_stores_on_brand_id"
    t.index ["store_id"], name: "index_brands_stores_on_store_id"
  end

  create_table "carts", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "subtotal", default: 0
  end

  create_table "categories", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.integer "old_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "brand_id", null: false
    t.integer "products_count", default: 0
    t.index ["brand_id"], name: "index_categories_on_brand_id"
  end

  create_table "countries", force: :cascade do |t|
    t.string "name", null: false
    t.string "abbreviation", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency"
  end

  create_table "data_migrations", primary_key: "version", id: :string, force: :cascade do |t|
  end

  create_table "flipper_features", force: :cascade do |t|
    t.string "key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_flipper_features_on_key", unique: true
  end

  create_table "flipper_gates", force: :cascade do |t|
    t.string "feature_key", null: false
    t.string "key", null: false
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["feature_key", "key", "value"], name: "index_flipper_gates_on_feature_key_and_key_and_value", unique: true
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["sluggable_type", "sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_type_and_sluggable_id"
  end

  create_table "fulfillments", force: :cascade do |t|
    t.datetime "begins_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.integer "order_count"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "line_items", force: :cascade do |t|
    t.integer "quantity", default: 1, null: false
    t.boolean "gift_card", default: false, null: false
    t.bigint "product_id"
    t.bigint "cart_id"
    t.bigint "order_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "points", default: 0
    t.index ["cart_id"], name: "index_line_items_on_cart_id"
    t.index ["order_id"], name: "index_line_items_on_order_id"
    t.index ["product_id"], name: "index_line_items_on_product_id"
  end

  create_table "maintenance_tasks_runs", force: :cascade do |t|
    t.string "task_name", null: false
    t.datetime "started_at", precision: nil
    t.datetime "ended_at", precision: nil
    t.float "time_running", default: 0.0, null: false
    t.integer "tick_count", default: 0, null: false
    t.integer "tick_total"
    t.string "job_id"
    t.bigint "cursor"
    t.string "status", default: "enqueued", null: false
    t.string "error_class"
    t.string "error_message"
    t.text "backtrace"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "arguments"
    t.integer "lock_version", default: 0, null: false
    t.index ["task_name", "created_at"], name: "index_maintenance_tasks_runs_on_task_name_and_created_at", order: { created_at: :desc }
  end

  create_table "messages", force: :cascade do |t|
    t.string "subject"
    t.string "messageable_type"
    t.bigint "messageable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "sent_at", precision: nil
    t.boolean "read", default: false
    t.datetime "read_at", precision: nil
    t.datetime "send_on", precision: nil
    t.index ["messageable_type", "messageable_id"], name: "index_messages_on_messageable"
  end

  create_table "notes", force: :cascade do |t|
    t.bigint "user_id"
    t.string "noteable_type"
    t.bigint "noteable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["noteable_type", "noteable_id"], name: "index_notes_on_noteable"
    t.index ["user_id"], name: "index_notes_on_user_id"
  end

  create_table "noticed_events", force: :cascade do |t|
    t.string "type"
    t.string "record_type"
    t.bigint "record_id"
    t.jsonb "params"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "notifications_count"
    t.index ["record_type", "record_id"], name: "index_noticed_events_on_record"
  end

  create_table "noticed_notifications", force: :cascade do |t|
    t.string "type"
    t.bigint "event_id", null: false
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.datetime "read_at", precision: nil
    t.datetime "seen_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["event_id"], name: "index_noticed_notifications_on_event_id"
    t.index ["recipient_type", "recipient_id"], name: "index_noticed_notifications_on_recipient"
  end

  create_table "notifications", force: :cascade do |t|
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.string "type", null: false
    t.jsonb "params"
    t.datetime "read_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["read_at"], name: "index_notifications_on_read_at"
    t.index ["recipient_type", "recipient_id"], name: "index_notifications_on_recipient"
  end

  create_table "orders", force: :cascade do |t|
    t.text "notes"
    t.integer "points", default: 0, null: false
    t.integer "status", default: 0
    t.date "approved_at"
    t.bigint "user_id", null: false
    t.bigint "admin_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "ahoy_visit_id"
    t.bigint "brand_id", null: false
    t.bigint "region_id", null: false
    t.string "sap_id"
    t.integer "ship_to", default: 0, null: false
    t.datetime "declined_at"
    t.index ["admin_id"], name: "index_orders_on_admin_id"
    t.index ["brand_id"], name: "index_orders_on_brand_id"
    t.index ["region_id"], name: "index_orders_on_region_id"
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "prices", force: :cascade do |t|
    t.integer "msrp_in_cents", default: 0, null: false
    t.integer "points_needed", default: 0, null: false
    t.integer "points_earned", default: 0, null: false
    t.bigint "country_id"
    t.bigint "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["country_id", "product_id"], name: "index_prices_on_country_id_and_product_id", unique: true
    t.index ["country_id"], name: "index_prices_on_country_id"
    t.index ["product_id"], name: "index_prices_on_product_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.decimal "msrp", precision: 10, scale: 2
    t.integer "points_needed"
    t.integer "points_earned"
    t.string "sku"
    t.boolean "active", default: true
    t.jsonb "image_data", default: {}
    t.bigint "category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "upc"
    t.boolean "gift_card", default: false
    t.integer "gift_card_value", default: 0
    t.index ["category_id"], name: "index_products_on_category_id"
  end

  create_table "products_promotions", id: false, force: :cascade do |t|
    t.bigint "promotion_id", null: false
    t.bigint "product_id", null: false
    t.index ["product_id"], name: "index_products_promotions_on_product_id"
    t.index ["promotion_id"], name: "index_products_promotions_on_promotion_id"
  end

  create_table "promotion_locations", force: :cascade do |t|
    t.bigint "promotion_id"
    t.string "promotable_type"
    t.bigint "promotable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["promotable_type", "promotable_id"], name: "index_promotion_locations_on_promotable"
    t.index ["promotion_id"], name: "index_promotion_locations_on_promotion_id"
  end

  create_table "promotions", force: :cascade do |t|
    t.string "name"
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.integer "multiplier"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "promotions_stores", id: false, force: :cascade do |t|
    t.bigint "promotion_id", null: false
    t.bigint "store_id", null: false
    t.index ["promotion_id"], name: "index_promotions_stores_on_promotion_id"
    t.index ["store_id"], name: "index_promotions_stores_on_store_id"
  end

  create_table "regions", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "admin_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "users_count", default: 0, null: false
    t.index ["admin_id"], name: "index_regions_on_admin_id"
  end

  create_table "reports", force: :cascade do |t|
    t.integer "frequency", default: 0
    t.string "name"
    t.string "key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "sales", force: :cascade do |t|
    t.string "serial_number"
    t.datetime "sold_at", precision: nil
    t.text "notes"
    t.integer "points"
    t.integer "status", default: 0
    t.datetime "approved_at", precision: nil
    t.bigint "admin_id"
    t.bigint "user_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "origin", default: 0
    t.bigint "ahoy_visit_id"
    t.bigint "brand_id"
    t.bigint "region_id"
    t.bigint "promotion_id"
    t.index ["admin_id"], name: "index_sales_on_admin_id"
    t.index ["brand_id"], name: "index_sales_on_brand_id"
    t.index ["product_id"], name: "index_sales_on_product_id"
    t.index ["promotion_id"], name: "index_sales_on_promotion_id"
    t.index ["region_id"], name: "index_sales_on_region_id"
    t.index ["user_id"], name: "index_sales_on_user_id"
  end

  create_table "states", force: :cascade do |t|
    t.string "abbreviation"
    t.string "name"
    t.bigint "region_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "users_count", default: 0, null: false
    t.bigint "country_id"
    t.index ["abbreviation"], name: "index_states_on_abbreviation", unique: true
    t.index ["country_id"], name: "index_states_on_country_id"
    t.index ["region_id"], name: "index_states_on_region_id"
  end

  create_table "store_chains", force: :cascade do |t|
    t.string "name"
    t.integer "users_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "stores_count", default: 0
  end

  create_table "store_requests", force: :cascade do |t|
    t.string "name"
    t.string "store_name"
    t.string "street"
    t.string "city"
    t.string "zip"
    t.string "email"
    t.string "manager_name"
    t.integer "status", default: 0
    t.bigint "state_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "brand_id"
    t.index ["brand_id"], name: "index_store_requests_on_brand_id"
    t.index ["state_id"], name: "index_store_requests_on_state_id"
  end

  create_table "stores", force: :cascade do |t|
    t.string "name", null: false
    t.string "account_number"
    t.string "city"
    t.string "street"
    t.string "unit"
    t.string "zip"
    t.string "phone_number"
    t.boolean "verified"
    t.integer "old_store_id"
    t.bigint "state_id"
    t.bigint "account_type_id"
    t.bigint "account_channel_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "brand_id"
    t.integer "users_count", default: 0, null: false
    t.bigint "store_chain_id"
    t.integer "status", default: 0, null: false
    t.index ["account_channel_id"], name: "index_stores_on_account_channel_id"
    t.index ["account_type_id"], name: "index_stores_on_account_type_id"
    t.index ["brand_id"], name: "index_stores_on_brand_id"
    t.index ["state_id"], name: "index_stores_on_state_id"
    t.index ["store_chain_id"], name: "index_stores_on_store_chain_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.string "username"
    t.string "last_name"
    t.string "phone_number"
    t.string "city"
    t.integer "points_earned"
    t.boolean "approved", default: false
    t.date "approved_at"
    t.integer "old_user_id"
    t.string "slug"
    t.bigint "state_id"
    t.bigint "store_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "avatar_data"
    t.integer "role", default: 0
    t.string "store_zip"
    t.integer "status", default: 0
    t.bigint "admin_region_id"
    t.jsonb "settings", default: {"sale_new"=>"0", "user_new"=>"0", "order_new"=>"0", "sale_updated"=>"0", "order_updated"=>"0", "user_approved"=>"0", "store_request_new"=>"0"}
    t.bigint "admin_brand_id"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "time_zone"
    t.boolean "failed_email", default: false
    t.text "street_ciphertext"
    t.text "zip_ciphertext"
    t.text "first_name_ciphertext"
    t.integer "email_frequency", default: 0
    t.datetime "last_seen"
    t.string "first_name"
    t.index ["admin_brand_id"], name: "index_users_on_admin_brand_id"
    t.index ["admin_region_id"], name: "index_users_on_admin_region_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["slug"], name: "index_users_on_slug", unique: true
    t.index ["state_id"], name: "index_users_on_state_id"
    t.index ["store_id"], name: "index_users_on_store_id"
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type"
    t.string "{:null=>false}"
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object"
    t.datetime "created_at", precision: nil
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "wallets", force: :cascade do |t|
    t.integer "current_balance", default: 0
    t.integer "available_balance", default: 0
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_wallets_on_user_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "addresses", "countries"
end
