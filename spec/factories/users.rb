# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  approved               :boolean          default(FALSE)
#  approved_at            :date
#  avatar_data            :jsonb
#  city                   :string
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  email_frequency        :integer          default("daily")
#  encrypted_password     :string           default(""), not null
#  failed_email           :boolean          default(FALSE)
#  first_name_ciphertext  :text
#  last_name              :string
#  last_seen              :datetime
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  phone_number           :string
#  points_earned          :integer
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular_user")
#  settings               :jsonb
#  sign_in_count          :integer          default(0), not null
#  slug                   :string
#  status                 :integer          default("new")
#  store_zip              :string
#  street_ciphertext      :text
#  time_zone              :string
#  unconfirmed_email      :string
#  username               :string
#  zip_ciphertext         :text
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  admin_brand_id         :bigint
#  admin_region_id        :bigint
#  old_user_id            :integer
#  state_id               :bigint
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_admin_brand_id        (admin_brand_id)
#  index_users_on_admin_region_id       (admin_region_id)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_users_on_slug                  (slug) UNIQUE
#  index_users_on_state_id              (state_id)
#  index_users_on_store_id              (store_id)
#
FactoryBot.define do
  factory :user do
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    email { Faker::Internet.email }
    password { "password123" }
    password_confirmation { "password123" }
    phone_number { Faker::PhoneNumber.phone_number }
    confirmed_at { Time.current }
    status { :active }

    # Create wallet after user is saved
    after(:create) do |user|
      create(:wallet, user: user)

      # Create address if user doesn't have one through store
      create(:address, addressable: user)
    end
  end

  factory :admin, class: "User" do
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    email { Faker::Internet.email }
    phone_number { Faker::PhoneNumber.phone_number }
    password { Faker::Internet.password(min_length: 10) }
    role { "super_admin" }
    confirmed_at { Time.current }
    status { "active" }
    approved { true }
    approved_at { Date.today }
    store
    address
  end
end
