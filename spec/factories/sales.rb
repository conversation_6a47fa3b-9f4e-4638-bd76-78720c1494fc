# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  notes         :text
#  origin        :integer          default("internet")
#  points        :integer
#  serial_number :string
#  sold_at       :datetime
#  status        :integer          default("pending")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint
#  ahoy_visit_id :bigint
#  brand_id      :bigint
#  product_id    :bigint           not null
#  promotion_id  :bigint
#  region_id     :bigint
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_admin_id      (admin_id)
#  index_sales_on_brand_id      (brand_id)
#  index_sales_on_product_id    (product_id)
#  index_sales_on_promotion_id  (promotion_id)
#  index_sales_on_region_id     (region_id)
#  index_sales_on_user_id       (user_id)
#
FactoryBot.define do
  # this factory is without promotion
  factory :sale do
    sold_at { Time.current }
    serial_number { Faker::Number.number(digits: 10) }
    product
    user
    region
    brand

    factory :approved_sale do
      status { "approved" }
    end

    factory :declined_sale do
      status { "declined" }
    end
  end
end
