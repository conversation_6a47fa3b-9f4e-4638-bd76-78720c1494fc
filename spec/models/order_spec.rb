# == Schema Information
#
# Table name: orders
#
#  id            :bigint           not null, primary key
#  approved_at   :date
#  declined_at   :datetime
#  notes         :text
#  points        :integer          default(0), not null
#  ship_to       :integer          default("home"), not null
#  status        :integer          default("pending")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint
#  ahoy_visit_id :bigint
#  brand_id      :bigint           not null
#  region_id     :bigint           not null
#  sap_id        :string
#  user_id       :bigint           not null
#
# Indexes
#
#  index_orders_on_admin_id   (admin_id)
#  index_orders_on_brand_id   (brand_id)
#  index_orders_on_region_id  (region_id)
#  index_orders_on_user_id    (user_id)
#
require "rails_helper"

RSpec.describe Order, type: :model do
  describe "Validations" do
    it { is_expected.to validate_presence_of(:user) }
    it { is_expected.to validate_presence_of(:brand) }
    it { is_expected.to validate_presence_of(:region) }
    it { is_expected.to validate_presence_of(:points) }
  end

  describe "Associations" do
    it { is_expected.to belong_to(:user) }
    it { is_expected.to belong_to(:admin).class_name("User").optional }
    it { is_expected.to belong_to(:brand) }
    it { is_expected.to belong_to(:region) }
    it { is_expected.to have_many(:line_items).dependent(:destroy) }
    it { is_expected.to have_many(:notations).as(:noteable).dependent(:destroy).class_name("Note") }
    it { is_expected.to have_one(:activity).as(:transactable).dependent(:destroy) }
  end

  describe "Enums" do
    it { is_expected.to define_enum_for(:status).with_values(pending: 0, processed: 1, shipped: 2, canceled: 3, approved: 4, declined: 5) }
    it { is_expected.to define_enum_for(:ship_to).with_values(home: 0, work: 1) }
  end

  describe "Delegations" do
    it { is_expected.to delegate_method(:name).to(:user).with_prefix(true) }
    it { is_expected.to delegate_method(:name).to(:admin).with_prefix(true).allow_nil }
    it { is_expected.to delegate_method(:name).to(:brand).with_prefix(true) }
  end

  describe "Callbacks" do
    let(:order) { build(:order) }

    it "notifies admins after creation" do
      expect(order).to receive(:notify_admins)
      order.save
    end

    it "notifies user after update" do
      order = create(:order)
      expect(order).to receive(:notify_user)
      order.update(status: "approved")
    end

    it "sets activity after creation" do
      expect(order).to receive(:set_activity)
      order.save
    end

    it "updates timestamps when status changes to approved" do
      order = create(:order)
      expect {
        order.update(status: "approved")
      }.to change { order.approved_at }.from(nil)
    end

    it "updates timestamps when status changes to declined" do
      order = create(:order)
      expect {
        order.update(status: "declined")
      }.to change { order.declined_at }.from(nil)
    end
  end

  describe "#name" do
    let(:user) { build(:user, first_name: "John", last_name: "Doe") }
    let(:order) { build(:order, user: user, created_at: Time.new(2023, 1, 15)) }

    it "returns a formatted string with created_at date and user name" do
      expect(order.name).to eq("Order by John Doe on 2023-01-15 00:00:00 UTC")
    end
  end

  describe "MeiliSearch" do
    it "includes the expected attributes" do
      expect(Order.meilisearch_settings[:filterable_attributes]).to include(:status, :brand_name, :created_at, :user_id, :brand_id, :region_id, :country_name)
      expect(Order.meilisearch_settings[:sortable_attributes]).to include(:created_at)
    end

    it "has a meilisearch_import scope" do
      expect(Order.meilisearch_import.to_sql).to include("INNER JOIN")
    end
  end
end
