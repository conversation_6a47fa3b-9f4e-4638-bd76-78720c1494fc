# == Schema Information
#
# Table name: regions
#
#  id          :bigint           not null, primary key
#  name        :string           not null
#  users_count :integer          default(0), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  admin_id    :bigint
#
# Indexes
#
#  index_regions_on_admin_id  (admin_id)
#
require "rails_helper"

RSpec.describe Region, type: :model do
  describe "Associations" do
    it { is_expected.to have_many(:states) }
    it { is_expected.to have_many(:users).through(:states) }
    it { is_expected.to have_many(:messages) }
    it { is_expected.to have_one(:admin).class_name("User").with_foreign_key(:admin_region_id) }
  end
end
