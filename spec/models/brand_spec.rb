# == Schema Information
#
# Table name: brands
#
#  id                 :bigint           not null, primary key
#  description        :text
#  name               :string           not null
#  optics_sales_count :integer
#  photo_sales_count  :integer
#  users_count        :integer          default(0), not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
# Indexes
#
#  index_brands_on_name  (name)
#
require "rails_helper"

RSpec.describe Brand, type: :model do
  describe "Validations" do
    subject { build(:brand) }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
  end

  describe "Associations" do
    it { is_expected.to have_many(:stores) }
    it { is_expected.to have_many(:categories) }
    it { is_expected.to have_many(:users).through(:stores) }
    it { is_expected.to have_many(:messages) }
  end
end
