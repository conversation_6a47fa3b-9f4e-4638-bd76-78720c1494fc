# == Schema Information
#
# Table name: products
#
#  id              :bigint           not null, primary key
#  active          :boolean          default(TRUE)
#  description     :text
#  gift_card       :boolean          default(FALSE)
#  gift_card_value :integer          default(0)
#  image_data      :jsonb
#  msrp            :decimal(10, 2)
#  name            :string
#  points_earned   :integer
#  points_needed   :integer
#  sku             :string
#  upc             :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  category_id     :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#
require "rails_helper"

RSpec.describe Product, type: :model do
  describe "Validations" do
    it "is valid with valid attributes" do
      product = build(:product)
      product.valid?
      expect(product).to be_valid
    end

    it "is not valid without a name" do
      product = build(:product, name: nil)
      product.valid?
      expect(product).to be_invalid
    end

    it "is not valid without a description" do
      product = build(:product, description: nil)
      product.valid?
      expect(product).to be_invalid
    end

    it "is not valid without a sku" do
      product = build(:product, sku: nil)
      product.valid?
      expect(product).to be_invalid
    end

    it "is not valid without a category" do
      product = build(:product, category_id: nil)
      product.valid?
      expect(product).to be_invalid
    end
  end

  describe "Associations" do
    it "belongs to category" do
      product = Product.reflect_on_association(:category)
      expect(product.macro).to eq(:belongs_to)
    end
  end
end
