class DashboardController < ApplicationController
  before_action :authenticate_user_with_sign_up!
  before_action :verify_point_account
  before_action :set_variant

  def help
    @help = Help.new(help_params)

    HelpMailer.with(name: @help.name, email: @help.email, message: @help.message, help_type: @help.help_type).enquiry.deliver_later
    redirect_to root_path, notice: "Your enquiry has been sent. We will get back to you shortly."
  end

  def help_form
    @help = Help.new
    @help.name = current_user.name
    @help.email = current_user.email
  end

  private

  def help_params
    params.require(:help).permit(:name, :email, :message, :help_type)
  end

  def verify_point_account
    if user_signed_in? && current_user.wallet.nil?
      current_user.create_wallet
    end
  end

  def set_variant
    if current_user.admin?
      request.variant = :admin
    elsif current_user.regular_user?
      request.variant = :regular
    end
  end
end
