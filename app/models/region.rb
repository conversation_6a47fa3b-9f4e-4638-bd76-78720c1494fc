# == Schema Information
#
# Table name: regions
#
#  id          :bigint           not null, primary key
#  name        :string           not null
#  users_count :integer          default(0), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  admin_id    :bigint
#
# Indexes
#
#  index_regions_on_admin_id  (admin_id)
#
class Region < ApplicationRecord
  has_many :states
  has_many :users, through: :states
  has_many :messages, as: :messageable

  has_one :admin, -> { where(role: :admin) }, class_name: "User", foreign_key: :admin_region_id

  scope :order_by_name, -> { order(:name) }

  def name_with_city
    name
  end

  def self.group_name
    "Region"
  end
end
