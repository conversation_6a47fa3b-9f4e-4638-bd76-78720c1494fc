<%= render Page::Component.new do |page| %>
  <% page.with_header title: "Welcome #{current_user.name.titleize}" %>
  <div class="grid">
    <div class="space-x-6 justify-self-center">
      <%= link_to "New Sale", new_sale_path, class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-xs shadow-zeiss-500 text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
      <%= link_to "Point Shop", shop_path, class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-xs shadow-zeiss-500 text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
    </div>
  </div>
  <div class="space-y-8">
    <div>
      <div class="pb-5 mb-4 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Sales</h3>
        <div class="flex mt-3 sm:mt-0 sm:ml-4">
          <%= link_to "More", sales_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
      <%= turbo_frame_tag "sales_results", src: sales_path(limit: 5) %>
    </div>
    <div>
      <div class="pb-5 mb-4 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Orders</h3>
        <div class="flex mt-3 sm:mt-0 sm:ml-4">
          <%= link_to "More", orders_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
      <%= turbo_frame_tag "orders_results", src: orders_path(limit: 5) %>
    </div>
  </divs>
<% end %>
